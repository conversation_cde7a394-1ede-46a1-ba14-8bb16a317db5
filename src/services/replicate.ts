import Replicate from 'replicate';
import { Model } from '@/types';

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export interface ReplicateGenerationParams {
  prompt: string;
  model: Model;
  // Common parameters for all LoRA models
  lora_scale?: number;
  megapixels?: string;
  num_outputs?: number;
  aspect_ratio?: string;
  output_format?: string;
  guidance_scale?: number;
  output_quality?: number;
  prompt_strength?: number;
  extra_lora_scale?: number;
  num_inference_steps?: number;
  go_fast?: boolean;
}

export interface ReplicateGenerationResult {
  imageUrl: string;
  generationTime: string;
  prompt: string;
  predictionId?: string;
}

// Default parameters for LoRA generation
const getDefaultParams = (modelId: string) => {
  const baseParams = {
    lora_scale: 1,
    megapixels: "1",
    num_outputs: 1,
    aspect_ratio: "1:1",
    output_format: "webp",
    guidance_scale: 3,
    output_quality: 80,
    prompt_strength: 0.8,
    extra_lora_scale: 1,
    num_inference_steps: 28,
    go_fast: false,
  };

  // Model-specific parameters
  switch (modelId) {
    case 'berserk':
      return {
        ...baseParams,
        model: "dev", // Required for b3rs3rk model
      };
    case 'zorby':
    case 'ascii':
    default:
      return baseParams;
  }
};

export async function generateWithReplicate(params: ReplicateGenerationParams): Promise<ReplicateGenerationResult> {
  const { prompt, model, ...customParams } = params;
  
  if (!model.replicateModel) {
    throw new Error(`No Replicate model configured for ${model.id}`);
  }

  const startTime = Date.now();
  
  try {
    // Get default parameters for this model
    const defaultParams = getDefaultParams(model.id);
    
    // Merge with custom parameters
    const input = {
      ...defaultParams,
      ...customParams,
      prompt,
    };

    // Determine the model identifier
    const modelIdentifier = model.replicateVersion && model.replicateVersion !== 'latest'
      ? `${model.replicateModel}:${model.replicateVersion}`
      : model.replicateModel;

    console.log(`Generating with model: ${modelIdentifier}`);
    console.log('Input parameters:', input);

    // Run the prediction
    const output = await replicate.run(modelIdentifier as `${string}/${string}:${string}`, { input });
    
    const endTime = Date.now();
    const generationTime = ((endTime - startTime) / 1000).toFixed(1);

    // Extract the image URL from the output
    let originalImageUrl: string;
    if (Array.isArray(output) && output.length > 0) {
      originalImageUrl = output[0];
    } else if (typeof output === 'string') {
      originalImageUrl = output;
    } else {
      throw new Error('Unexpected output format from Replicate');
    }

    // Use proxy URL to avoid CORS issues
    const imageUrl = `/api/proxy-image?url=${encodeURIComponent(originalImageUrl)}`;

    console.log('Original image URL:', originalImageUrl);
    console.log('Proxied image URL:', imageUrl);

    return {
      imageUrl,
      generationTime: `${generationTime} seconds`,
      prompt,
    };
  } catch (error) {
    console.error('Replicate generation error:', error);
    throw new Error(`Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to validate API token
export function validateReplicateToken(): boolean {
  return !!process.env.REPLICATE_API_TOKEN;
}
