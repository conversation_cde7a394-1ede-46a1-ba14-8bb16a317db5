import { createWalletClient, custom } from 'viem';
import { baseSepolia } from 'viem/chains';
import { wrapFetchWithPayment, decodeXPaymentResponse } from 'x402-fetch';
import type { Account } from 'viem';

// USDC contract address on Base Sepolia
const USDC_CONTRACT_ADDRESS = '******************************************';

// Payment recipient address
const PAYMENT_RECIPIENT = '******************************************';

// USDC contract ABI (minimal for transfer) - keeping for reference but not used
// const USDC_ABI = [
//   {
//     "inputs": [
//       {"internalType": "address", "name": "to", "type": "address"},
//       {"internalType": "uint256", "name": "amount", "type": "uint256"}
//     ],
//     "name": "transfer",
//     "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
//     "stateMutability": "nonpayable",
//     "type": "function"
//   },
//   {
//     "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
//     "name": "balanceOf",
//     "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
//     "stateMutability": "view",
//     "type": "function"
//   }
// ] as const;

// Type for window.ethereum to avoid 'any' usage
type WindowEthereum = {
  request: (args: { method: string; params?: unknown[] }) => Promise<unknown>;
  isMetaMask?: boolean;
};

export interface X402PaymentResult {
  success: boolean;
  paymentResponse?: Record<string, unknown>;
  error?: string;
}

export interface X402PaymentRequest {
  url: string;
  amount: string; // e.g., "$1"
  description?: string;
}

// Create a mock x402 payment endpoint for testing
export const createX402PaymentEndpoint = (baseUrl: string) => {
  return `${baseUrl}/api/x402-payment`;
};

// Process x402 payment using the account
export async function processX402Payment(
  account: Account,
  paymentRequest: X402PaymentRequest
): Promise<X402PaymentResult> {
  try {
    // Wrap fetch with x402 payment capability
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const fetchWithPayment = wrapFetchWithPayment(fetch, account as any);
    
    // Make the payment request
    const response = await fetchWithPayment(paymentRequest.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: paymentRequest.amount,
        description: paymentRequest.description || 'LoRA image generation payment',
      }),
    });

    if (!response.ok) {
      throw new Error(`Payment failed: ${response.status} ${response.statusText}`);
    }

    await response.json();
    
    // Decode payment response from headers
    const paymentResponseHeader = response.headers.get('x-payment-response');
    const paymentResponse = paymentResponseHeader 
      ? decodeXPaymentResponse(paymentResponseHeader)
      : null;

    return {
      success: true,
      paymentResponse: paymentResponse || undefined,
    };
  } catch (error) {
    console.error('x402 payment error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment failed',
    };
  }
}

// Get wallet client from browser
export function getWalletClient(): Account | null {
  // SSR guard - ensure we're in browser environment
  if (typeof window === 'undefined' || !(window as { ethereum?: WindowEthereum }).ethereum) {
    return null;
  }

  try {
    const walletClient = createWalletClient({
      chain: baseSepolia,
      transport: custom((window as { ethereum?: WindowEthereum }).ethereum!)
    });

    // For x402, we need to create an account-like object
    // This is a simplified approach - in production you'd want proper account handling
    return walletClient as unknown as Account;
  } catch (error) {
    console.error('Failed to create wallet client:', error);
    return null;
  }
}

// Real USDC payment to specified wallet using direct MetaMask calls
export async function processRealX402Payment(
  amount: string,
  description?: string
): Promise<X402PaymentResult> {
  try {
    // SSR guard - ensure we're in browser environment with MetaMask
    if (typeof window === 'undefined' || !(window as { ethereum?: WindowEthereum }).ethereum) {
      throw new Error('MetaMask not available');
    }

    console.log('Starting payment process...');

    // Request account access
    const accounts = await (window as { ethereum?: WindowEthereum }).ethereum!.request({
      method: 'eth_requestAccounts'
    }) as string[];

    if (!accounts || accounts.length === 0) {
      throw new Error('No accounts available');
    }

    const userAccount = accounts[0];
    console.log('Connected account:', userAccount);
    console.log('Sending payment to:', PAYMENT_RECIPIENT);

    // Check network and switch if needed
    const chainId = await (window as { ethereum?: WindowEthereum }).ethereum!.request({ method: 'eth_chainId' }) as string;
    const baseSepoliaChainId = '0x14a34'; // 84532 in decimal

    if (chainId !== baseSepoliaChainId) {
      try {
        await (window as { ethereum?: WindowEthereum }).ethereum!.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: baseSepoliaChainId }],
        });
      } catch (switchError: unknown) {
        if ((switchError as { code?: number }).code === 4902) {
          await (window as { ethereum?: WindowEthereum }).ethereum!.request({
            method: 'wallet_addEthereumChain',
            params: [{
              chainId: baseSepoliaChainId,
              chainName: 'Base Sepolia',
              nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
              rpcUrls: ['https://sepolia.base.org'],
              blockExplorerUrls: ['https://sepolia-explorer.base.org'],
            }],
          });
        } else {
          throw switchError;
        }
      }
    }

    // Prepare USDC transfer transaction data
    const usdcAmount = '0xF4240'; // 1,000,000 in hex (1 USDC with 6 decimals)

    // ERC20 transfer function signature: transfer(address,uint256)
    const transferSignature = '0xa9059cbb';
    const recipientPadded = PAYMENT_RECIPIENT.slice(2).padStart(64, '0');
    const amountPadded = usdcAmount.slice(2).padStart(64, '0');
    const data = transferSignature + recipientPadded + amountPadded;

    console.log('Sending 1 USDC to:', PAYMENT_RECIPIENT);

    // Send transaction via MetaMask
    const txHash = await (window as { ethereum?: WindowEthereum }).ethereum!.request({
      method: 'eth_sendTransaction',
      params: [{
        from: userAccount,
        to: USDC_CONTRACT_ADDRESS,
        data: data,
        gas: '0x5208', // 21000 gas
      }],
    }) as string;

    console.log('Transaction hash:', txHash);

    return {
      success: true,
      paymentResponse: {
        amount,
        description: description || 'LoRA image generation payment',
        timestamp: new Date().toISOString(),
        transactionId: txHash,
        account: userAccount,
        recipient: PAYMENT_RECIPIENT,
        usdcAmount: '1.0',
      },
    };
  } catch (error) {
    console.error('USDC payment error:', error);

    let errorMessage = 'Payment failed';
    if (error instanceof Error) {
      if (error.message.includes('User rejected') || error.message.includes('denied')) {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message.includes('insufficient funds')) {
        errorMessage = 'Insufficient USDC balance';
      } else if (error.message.includes('network')) {
        errorMessage = 'Network error - please check your connection';
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

// Mock x402 payment verification for development
export async function mockX402Payment(
  amount: string,
  description?: string
): Promise<X402PaymentResult> {
  // Simulate payment processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // For development, always return success
  return {
    success: true,
    paymentResponse: {
      amount,
      description,
      timestamp: new Date().toISOString(),
      transactionId: `mock_tx_${Date.now()}`,
    },
  };
}
