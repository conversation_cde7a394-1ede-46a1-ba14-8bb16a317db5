'use client';

import { GenerationResult } from '@/types';
import { Download, Flag, Eye, Wand2, Loader2 } from 'lucide-react';

interface PlaygroundOutputProps {
  result: GenerationResult | null;
  isGenerating: boolean;
}

export default function PlaygroundOutput({ result, isGenerating }: PlaygroundOutputProps) {
  if (isGenerating) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-gray-900">
        <Loader2 className="w-8 h-8 text-blue-500 animate-spin mb-4" />
        <p className="text-gray-400">Generating your image...</p>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-800 rounded-lg flex items-center justify-center mb-4">
            <Wand2 className="w-8 h-8 text-gray-600" />
          </div>
          <h3 className="text-white font-medium mb-2">Ready to Generate</h3>
          <p className="text-gray-400 text-sm">
            Enter a prompt and click Generate to create your image
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Generated Image */}
      <div className="flex-1 p-4">
        <div className="h-full bg-gray-800 rounded-lg overflow-hidden">
          <img
            src={result.imageUrl}
            alt={result.prompt}
            className="w-full h-full object-contain"
            crossOrigin="anonymous"
            onLoad={() => {
              console.log('Image loaded successfully:', result.imageUrl);
            }}
            onError={(e) => {
              console.error('Image failed to load:', result.imageUrl);
              console.error('Error details:', e);
              // Fallback to placeholder if image fails to load
              e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgdmlld0JveD0iMCAwIDUxMiA1MTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiBmaWxsPSIjMzc0MTUxIi8+Cjx0ZXh0IHg9IjI1NiIgeT0iMjU2IiBmaWxsPSIjOUNBNEFGIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPkdlbmVyYXRlZCBJbWFnZTwvdGV4dD4KPC9zdmc+';
            }}
          />
        </div>
      </div>

      {/* Generation Info */}
      <div className="p-4 border-t border-gray-800">
        <div className="mb-4">
          <p className="text-gray-400 text-sm mb-1">Generated in {result.generationTime}</p>
          <p className="text-white text-sm font-medium">&quot;{result.prompt}&quot;</p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <button className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors">
            <Wand2 className="w-4 h-4" />
            Tweak it
          </button>
          <button className="flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors">
            <Download className="w-4 h-4" />
            Download
          </button>
          <button className="flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors">
            <Flag className="w-4 h-4" />
            Report
          </button>
          <button className="flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors">
            <Eye className="w-4 h-4" />
            View full prediction
          </button>
        </div>
      </div>
    </div>
  );
}
