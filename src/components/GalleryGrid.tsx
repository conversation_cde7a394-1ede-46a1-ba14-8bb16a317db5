'use client';

import { galleryImages } from '@/data/gallery';
import { getModelById } from '@/data/models';

export default function GalleryGrid() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-white mb-8 uppercase tracking-wider">
        Gallery
      </h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {galleryImages.map((image) => {
          const model = getModelById(image.modelId);
          
          return (
            <div key={image.id} className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-750 transition-colors">
              {/* Image */}
              <div className="aspect-square bg-gray-700 relative overflow-hidden">
                <img 
                  src={image.imageUrl} 
                  alt={image.prompt}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    // Fallback to placeholder if image fails to load
                    e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjMzc0MTUxIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMjAwIiBmaWxsPSIjOUNBNEFGIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPkdlbmVyYXRlZCBJbWFnZTwvdGV4dD4KPC9zdmc+';
                  }}
                />
              </div>

              {/* Content */}
              <div className="p-4">
                {/* Model Badge */}
                {model && (
                  <div className="mb-2">
                    <span className="inline-block bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded">
                      {model.provider} / {model.name}
                    </span>
                  </div>
                )}

                {/* Prompt */}
                <p className="text-white text-sm font-medium mb-2 line-clamp-2">
                  {image.prompt}
                </p>

                {/* Generation Info */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Generated in {image.generationTime}</span>
                  <span>{new Date(image.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
