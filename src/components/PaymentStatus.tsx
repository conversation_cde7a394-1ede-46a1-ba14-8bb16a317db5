'use client'

import { CheckCircle, XCircle, Clock } from 'lucide-react'

interface PaymentStatusProps {
  status: 'idle' | 'processing' | 'success' | 'error'
  error?: string
  amount?: string
}

export default function PaymentStatus({ status, error, amount = '$1' }: PaymentStatusProps) {
  if (status === 'idle') return null

  const getStatusConfig = () => {
    switch (status) {
      case 'processing':
        return {
          icon: <Clock className="w-5 h-5 text-blue-500 animate-pulse" />,
          title: 'Processing Payment',
          message: `Processing ${amount} payment on Base Sepolia...`,
          bgColor: 'bg-blue-900/20',
          borderColor: 'border-blue-500/30'
        }
      case 'success':
        return {
          icon: <CheckCircle className="w-5 h-5 text-green-500" />,
          title: 'Payment Successful',
          message: `${amount} payment completed successfully!`,
          bgColor: 'bg-green-900/20',
          borderColor: 'border-green-500/30'
        }
      case 'error':
        return {
          icon: <XCircle className="w-5 h-5 text-red-500" />,
          title: 'Payment Failed',
          message: error || 'Payment processing failed. Please try again.',
          bgColor: 'bg-red-900/20',
          borderColor: 'border-red-500/30'
        }
      default:
        return null
    }
  }

  const config = getStatusConfig()
  if (!config) return null

  return (
    <div className={`p-4 rounded-lg border ${config.bgColor} ${config.borderColor} mb-4`}>
      <div className="flex items-center gap-3">
        {config.icon}
        <div>
          <h4 className="text-white font-medium text-sm">{config.title}</h4>
          <p className="text-gray-300 text-xs mt-1">{config.message}</p>
        </div>
      </div>
    </div>
  )
}
