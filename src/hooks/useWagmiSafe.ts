'use client';

import { useState, useEffect } from 'react';
import { useAccount, useConnect, useDisconnect } from 'wagmi';

/**
 * A safe wrapper around wagmi hooks that prevents hydration mismatches
 * by only returning wallet state after the component has mounted on the client
 */
export function useWagmiSafe() {
  const [mounted, setMounted] = useState(false);
  const account = useAccount();
  const connect = useConnect();
  const disconnect = useDisconnect();

  useEffect(() => {
    setMounted(true);
  }, []);

  // Return safe defaults until mounted
  if (!mounted) {
    return {
      mounted: false,
      address: undefined,
      isConnected: false,
      isConnecting: false,
      isDisconnected: true,
      connectors: [],
      connect: () => {},
      disconnect: () => {},
      isPending: false,
    };
  }

  // Return actual wagmi state after mounted
  return {
    mounted: true,
    address: account.address,
    isConnected: account.isConnected,
    isConnecting: account.isConnecting,
    isDisconnected: account.isDisconnected,
    connectors: connect.connectors,
    connect: connect.connect,
    disconnect: disconnect.disconnect,
    isPending: connect.isPending,
  };
}
